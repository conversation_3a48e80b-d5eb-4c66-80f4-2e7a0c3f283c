use std::sync::Arc;
use std::clone::<PERSON><PERSON>;

use anyhow::Result;
use chrono::{DateTime, Utc};
use tokio::time::{interval, Duration};
use tracing::{info, error};

use hypertrader_models::kline::interval::KlineInterval;
use hyperliquid_rust_sdk::AssetMeta;
use hypertrader_clickhouse::client::ClickHouseClient;
use hypertrader_clickhouse::init_clickhouse_client;

use crate::{optimizer::{TableOptimizer}, service::RangeService};

#[derive(Clone)]
pub struct BackfillOhlcService {
    clickhouse_client: ClickHouseClient,
    range_service: RangeService,
    days_to_fetch: i64,
    batch_size: usize,
}

impl BackfillOhlcService {
    pub async fn new(
        range_service: RangeService,
        days_to_fetch: i64,
        batch_size: usize,
    ) -> Result<Self> {
        let clickhouse_client: ClickHouseClient = init_clickhouse_client().await?;

        Ok(Self {
            clickhouse_client,
            range_service,
            days_to_fetch,
            batch_size,
        })
    }

    pub async fn fetch_and_update_recent_data(
        &self,
        subscribed_coins: Vec<AssetMeta>,
        batch_size: usize,
    ) -> Result<()> {
        if subscribed_coins.is_empty() {
            info!("No subscribed coins, skipping recent OHLC data retrieval");
            return Ok(());
        }

        info!(
            "Starting retrieval of recent (3-minute) OHLC data for {} coins, batch size: {}",
            subscribed_coins.len(),
            batch_size
        );

        // Calculate the 27-minute window
        let now = chrono::Utc::now();
        let start_time = now - chrono::Duration::minutes(30);
        let end_time = now - chrono::Duration::minutes(3);

        // Keep track of coins that still need processing
        let coins_to_process: dashmap::DashMap<String, ()> = dashmap::DashMap::new();
        for coin in &subscribed_coins {
            coins_to_process.insert(coin.name.clone(), ());
        }

        let mut retry_iters = 0;
        let max_retries = 3; // Fewer retries for recent data

        // Continue until all coins are processed or max retries reached
        while coins_to_process.len() > 0 && retry_iters < max_retries {
            retry_iters += 1;

            // Prepare coins list for processing
            let mut coins_to_fetch_vec: Vec<String> = coins_to_process
                .iter()
                .map(|entry| entry.key().clone())
                .collect();

            // Alternate order to distribute load
            if retry_iters % 2 == 0 {
                coins_to_fetch_vec.reverse();
            }

            // Process in batches
            for chunk in coins_to_fetch_vec.chunks(batch_size) {
                let mut tasks = Vec::with_capacity(batch_size);

                // Create tasks for current batch
                for symbol in chunk {
                    // Wait for the next interval to avoid overlapping
                    tokio::time::sleep(Duration::from_secs(1)).await;

                    let symbol = symbol.clone();
                    let coins_map = &coins_to_process;
                    let service = &self.range_service;

                    // Create a future for this coin's update
                    let task = async move {
                        match service.fetch_and_store_candles(
                            &symbol,
                            start_time,
                            end_time,
                        ).await {
                            Ok(count) => {
                                if count > 0 {
                                    // Successfully processed data, remove from pending list
                                    coins_map.remove(&symbol);
                                } else {
                                    info!(
                                        "No recent OHLC records found for {}",
                                        symbol
                                    );
                                }
                                Ok(())
                            },
                            Err(e) => Err(e)
                        }
                    };

                    tasks.push(task);
                }

                // Execute current batch tasks concurrently
                let _results = futures::future::join_all(tasks).await;
            }

            // Wait to ensure network stability for retries
            if coins_to_process.len() > 0 && retry_iters < max_retries {
                // Short wait time for recent data
                let wait_seconds = 2 * retry_iters;
                tokio::time::sleep(std::time::Duration::from_secs(wait_seconds as u64)).await;
            }
        }

        if coins_to_process.len() > 0 {
            info!(
                "Could not update recent OHLC data for {} coins after {} attempts",
                coins_to_process.len(),
                retry_iters
            );
        }

        let processed = subscribed_coins.len() - coins_to_process.len();
        info!(
            "Completed update of recent OHLC data for {}/{} coins",
            processed,
            subscribed_coins.len()
        );

        Ok(())
    }

    pub async fn update_materialized_views(&self) -> Result<()> {
        info!("Updating materialized views with new volume and trades calculation");

        // Define all intervals to update (excluding 3d for special handling)
        let intervals = [
            ("3m", "3", "MINUTE"),
            ("5m", "5", "MINUTE"),
            ("15m", "15", "MINUTE"),
            ("30m", "30", "MINUTE"),
            ("1h", "1", "HOUR"),
            ("2h", "2", "HOUR"),
            ("4h", "4", "HOUR"),
            ("8h", "8", "HOUR"),
            ("12h", "12", "HOUR"),
            ("1d", "1", "DAY"),
            ("1w", "1", "WEEK"),
            ("1M", "1", "MONTH"),
        ];

        for (interval_name, interval_duration, interval_unit) in intervals {
            // First drop the existing view
            let drop_view_query = format!("DROP VIEW IF EXISTS mv_ohlc_{}", interval_name);
            self.clickhouse_client.execute_query(&drop_view_query).await?;

            info!("Dropped view mv_ohlc_{}", interval_name);

            // Adjust time close expression based on interval unit
            let time_close_expr = match interval_unit {
                "WEEK" | "MONTH" => {
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 SECOND")
                },
                _ => {
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 MILLISECOND")
                }
            };

            // Create new materialized view with updated calculation
            let table_name = format!("ohlc_{}", interval_name);
            let view_name = format!("mv_ohlc_{}", interval_name);

            let create_view_query = format!(
                "CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} TO {table_name} AS
            SELECT
                toStartOfInterval(time_open, INTERVAL {interval_duration} {interval_unit}) AS time_open,
                {time_close_expr} AS time_close,
                symbol,
                argMinState(open, toDateTime64(ohlc_1m.time_open, 3, 'UTC')) AS open,
                max(high) AS high,
                min(low) AS low,
                argMaxState(close, toDateTime64(ohlc_1m.time_open, 3, 'UTC')) AS close,
                sum(total_volume * (toDateTime64(ohlc_1m.time_open, 3) >= toDateTime64(now() - INTERVAL 2 MINUTE, 3))) AS total_volume,
                sum(num_trades * (toDateTime64(ohlc_1m.time_open, 3) >= toDateTime64(now() - INTERVAL 2 MINUTE, 3))) AS num_trades
            FROM ohlc_1m
            GROUP BY time_open, time_close, symbol",
            );

            self.clickhouse_client.execute_query(&create_view_query).await?;
            info!("Created updated view mv_ohlc_{}", interval_name);
        }

        // Special handling for 3d interval to match TradingView standard
        // Drop existing 3d view
        let drop_view_query = "DROP VIEW IF EXISTS mv_ohlc_3d";
        self.clickhouse_client.execute_query(drop_view_query).await?;
        info!("Dropped view mv_ohlc_3d");

        // TradingView 3d calculation: Group every 3 days starting from Unix epoch (Thursday)
        // This ensures consistent 3-day grouping across all time periods
        let time_open_expr = "toStartOfDay(toDateTime64(time_open, 3)) - INTERVAL (toDaysSinceYearZero(toDate(time_open)) % 3) DAY";
        let time_close_expr = "toStartOfDay(toDateTime64(time_open, 3)) - INTERVAL (toDaysSinceYearZero(toDate(time_open)) % 3) DAY + INTERVAL 3 DAY - INTERVAL 1 SECOND";

        let create_3d_view_query = format!(
            "CREATE MATERIALIZED VIEW IF NOT EXISTS mv_ohlc_3d TO ohlc_3d AS
            SELECT
                {time_open_expr} AS time_open,
                {time_close_expr} AS time_close,
                symbol,
                argMinState(open, toDateTime64(ohlc_1m.time_open, 3, 'UTC')) AS open,
                max(high) AS high,
                min(low) AS low,
                argMaxState(close, toDateTime64(ohlc_1m.time_open, 3, 'UTC')) AS close,
                sum(total_volume * (toDateTime64(ohlc_1m.time_open, 3) >= toDateTime64(now() - INTERVAL 2 MINUTE, 3))) AS total_volume,
                sum(num_trades * (toDateTime64(ohlc_1m.time_open, 3) >= toDateTime64(now() - INTERVAL 2 MINUTE, 3))) AS num_trades
            FROM ohlc_1m
            GROUP BY time_open, time_close, symbol"
        );

        self.clickhouse_client.execute_query(&create_3d_view_query).await?;
        info!("Created updated view mv_ohlc_3d with TradingView standard grouping");

        info!("All materialized views updated successfully");
        Ok(())
    }

    pub async fn update_klines_materialized_views(&self) -> Result<()> {
        info!("Creating materialized views from klines table to ohlc_* tables");

        // Part 1: Direct mapping for intervals 3m -> 1d (keep current approach)
        let direct_intervals = [
            ("3m", "3", "MINUTE"),
            ("5m", "5", "MINUTE"),
            ("15m", "15", "MINUTE"),
            ("30m", "30", "MINUTE"),
            ("1h", "1", "HOUR"),
            ("2h", "2", "HOUR"),
            ("4h", "4", "HOUR"),
            ("8h", "8", "HOUR"),
            ("12h", "12", "HOUR"),
            ("1d", "1", "DAY"),
        ];

        info!("Creating direct mapping views for intervals 3m -> 1d");
        for (interval_name, _interval_duration, _interval_unit) in direct_intervals {
            // First drop the existing view if it exists
            let drop_view_query = format!("DROP VIEW IF EXISTS mv_klines_to_ohlc_{}", interval_name);
            self.clickhouse_client.execute_query(&drop_view_query).await?;

            info!("Dropped view mv_klines_to_ohlc_{}", interval_name);

            // Create a new materialized view that maps directly from klines to the appropriate ohlc table
            let table_name = format!("ohlc_{}", interval_name);
            let view_name = format!("mv_klines_to_ohlc_{}", interval_name);

            let create_view_query = format!(
                "CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} TO {table_name} AS
                SELECT
                    time_open,
                    time_close,
                    symbol,
                    argMinState(open, toDateTime64(time_open, 3, 'UTC')) AS open,
                    max(high) AS high,
                    min(low) AS low,
                    argMaxState(close, toDateTime64(time_close, 3, 'UTC')) AS close,
                    sum(volume) AS total_volume,
                    sum(num_trades) AS num_trades
                FROM klines
                WHERE interval = '{interval_name}'
                GROUP BY time_open, time_close, symbol",
            );

            self.clickhouse_client.execute_query(&create_view_query).await?;
            info!("Created direct mapping view mv_klines_to_ohlc_{}", interval_name);
        }

        // Part 2: Aggregated views for intervals 1d -> 3d (aggregate from 1d data)
        let aggregated_intervals_3d = [
            ("3d", "3", "DAY"),
        ];

        info!("Creating aggregated views for intervals 1d -> 3d from 1d data");
        for (interval_name, interval_duration, interval_unit) in aggregated_intervals_3d {
            // First drop the existing view if it exists
            let drop_view_query = format!("DROP VIEW IF EXISTS mv_klines_to_ohlc_{}", interval_name);
            self.clickhouse_client.execute_query(&drop_view_query).await?;
        }

        // TradingView 3d calculation: Group every 3 days starting from Unix epoch (Thursday)
        // This ensures consistent 3-day grouping across all time periods
        let time_open_expr = format!(
            "toStartOfDay(toDateTime64(time_open, 3)) - INTERVAL (toDaysSinceYearZero(toDate(time_open)) % 3) DAY"
        );
        let time_close_expr = format!(
            "toStartOfDay(toDateTime64(time_open, 3)) - INTERVAL (toDaysSinceYearZero(toDate(time_open)) % 3) DAY + INTERVAL 3 DAY - INTERVAL 1 SECOND"
        );

        let create_view_query = format!(
            "CREATE MATERIALIZED VIEW IF NOT EXISTS mv_klines_to_ohlc_3d TO ohlc_3d AS
            SELECT
                {time_open_expr} AS time_open,
                {time_close_expr} AS time_close,
                symbol,
                argMinState(open, toDateTime64(klines.time_open, 3, 'UTC')) AS open,
                max(high) AS high,
                min(low) AS low,
                argMaxState(close, toDateTime64(klines.time_open, 3, 'UTC')) AS close,
                sum(volume) AS total_volume,
                sum(num_trades) AS num_trades
            FROM klines
            WHERE interval = '1d'
            GROUP BY time_open, time_close, symbol",
        );
        self.clickhouse_client.execute_query(&create_view_query).await?;
        info!("Created aggregated view mv_klines_to_ohlc_3d from 1d data");

        // Part 3: Aggregated views for intervals 1w -> 1M (aggregate from 1d data)
        let aggregated_intervals = [
            ("1w", "1", "WEEK"),
            ("1M", "1", "MONTH"),
        ];

        info!("Creating aggregated views for intervals 1w -> 1M from 1d data");
        for (interval_name, interval_duration, interval_unit) in aggregated_intervals {
            // First drop the existing view if it exists
            let drop_view_query = format!("DROP VIEW IF EXISTS mv_klines_to_ohlc_{}", interval_name);
            self.clickhouse_client.execute_query(&drop_view_query).await?;

            info!("Dropped view mv_klines_to_ohlc_{}", interval_name);

            // Create time_close expression based on interval unit
            let time_close_expr = match interval_unit {
                "WEEK" | "MONTH" => {
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 SECOND")
                },
                _ => {
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 MILLISECOND")
                }
            };

            // Create a new materialized view that aggregates from 1d data
            let table_name = format!("ohlc_{}", interval_name);
            let view_name = format!("mv_klines_to_ohlc_{}", interval_name);

            let create_view_query = format!(
                "CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} TO {table_name} AS
                SELECT
                    toStartOfInterval(time_open, INTERVAL {interval_duration} {interval_unit}) AS time_open,
                    {time_close_expr} AS time_close,
                    symbol,
                    argMinState(open, toDateTime64(klines.time_open, 3, 'UTC')) AS open,
                    max(high) AS high,
                    min(low) AS low,
                    argMaxState(close, toDateTime64(klines.time_open, 3, 'UTC')) AS close,
                    sum(volume) AS total_volume,
                    sum(num_trades) AS num_trades
                FROM klines
                WHERE interval = '1d'
                GROUP BY time_open, time_close, symbol",
            );

            self.clickhouse_client.execute_query(&create_view_query).await?;
            info!("Created aggregated view mv_klines_to_ohlc_{} from 1d data", interval_name);
        }

        info!("All klines to ohlc materialized views created successfully");
        Ok(())
    }

    pub async fn run_periodic_ohlc_updates(
        &self,
        subscribed_coins: Vec<AssetMeta>
    ) -> Result<()> {
        // First update materialized views
        // self.update_materialized_views().await?;
        let table_optimizer = TableOptimizer::new().await?;

        // Setup interval for periodic updates (3 minutes)
        let mut interval = interval(Duration::from_secs(3 * 60));

        loop {
            interval.tick().await;
            info!("Running periodic OHLC data update");

            // Use the new method to fetch only recent 3-minute data
            match self.fetch_and_update_recent_data(
                subscribed_coins.clone(),
                self.batch_size
            ).await {
                Ok(_) => {
                    // Optimize the table after each update
                    table_optimizer.optimize_all_now().await?;
                    info!("Periodic recent OHLC data update completed successfully")
                },
                Err(e) => error!("Periodic recent OHLC data update failed: {}", e),
            }
        }
    }


    // VOLUME AND TRADES CALCULATION - INDIVIDUAL TIMEFRAMES
    pub async fn backfill_all_timeframe_volumes(&self, subscribed_coins: Vec<AssetMeta>) -> Result<()> {
        info!("Starting volume and trades backfill for all timeframes");

        if subscribed_coins.is_empty() {
            info!("No subscribed coins found, skipping volume backfill");
            return Ok(());
        }

        // Run backfills for each individual timeframe
        self.backfill_1M_timeframe(&subscribed_coins, 48).await?;
        self.backfill_1w_timeframe(&subscribed_coins, 100).await?;
        self.backfill_3d_timeframe(&subscribed_coins, 100).await?;
        self.backfill_1d_timeframe(&subscribed_coins, 100).await?;
        self.backfill_12h_timeframe(&subscribed_coins, 100).await?;
        self.backfill_8h_timeframe(&subscribed_coins, 100).await?;
        self.backfill_4h_timeframe(&subscribed_coins, 100).await?;
        self.backfill_2h_timeframe(&subscribed_coins, 100).await?;
        self.backfill_1h_timeframe(&subscribed_coins, 100).await?;
        self.backfill_30m_timeframe(&subscribed_coins, 100).await?;
        self.backfill_15m_timeframe(&subscribed_coins, 100).await?;
        self.backfill_5m_timeframe(&subscribed_coins, 100).await?;
        self.backfill_3m_timeframe(&subscribed_coins, 100).await?;

        info!("Completed volume and trades backfill for all timeframes");
        Ok(())
    }

    // 3-minute timeframe
    async fn backfill_3m_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "3m";
        let interval = KlineInterval::ThreeMinutes;

        // Calculate time window specifically for this timeframe
        let now = Utc::now();
        let lookback = chrono::Duration::minutes(3);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 5-minute timeframe
    async fn backfill_5m_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "5m";
        let interval = KlineInterval::FiveMinutes;

        let now = Utc::now();
        let lookback = chrono::Duration::minutes(5);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 15-minute timeframe
    async fn backfill_15m_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "15m";
        let interval = KlineInterval::FifteenMinutes;

        let now = Utc::now();
        let lookback = chrono::Duration::minutes(15);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 30-minute timeframe
    async fn backfill_30m_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "30m";
        let interval = KlineInterval::ThirtyMinutes;

        let now = Utc::now();
        let lookback = chrono::Duration::minutes(30);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 1-hour timeframe
    async fn backfill_1h_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "1h";
        let interval = KlineInterval::OneHour;

        let now = Utc::now();
        let lookback = chrono::Duration::hours(1);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 2-hour timeframe
    async fn backfill_2h_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "2h";
        let interval = KlineInterval::TwoHours;

        let now = Utc::now();
        let lookback = chrono::Duration::hours(2);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 4-hour timeframe
    async fn backfill_4h_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "4h";
        let interval = KlineInterval::FourHours;

        let now = Utc::now();
        let lookback = chrono::Duration::hours(4);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 8-hour timeframe
    async fn backfill_8h_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "8h";
        let interval = KlineInterval::EightHours;

        let now = Utc::now();
        let lookback = chrono::Duration::hours(8);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 12-hour timeframe
    async fn backfill_12h_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "12h";
        let interval = KlineInterval::TwelveHours;

        let now = Utc::now();
        let lookback = chrono::Duration::hours(12);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 1-day timeframe
    async fn backfill_1d_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "1d";
        let interval = KlineInterval::OneDay;

        let now = Utc::now();
        let lookback = chrono::Duration::days(1);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 3-day timeframe
    async fn backfill_3d_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "3d";
        let interval = KlineInterval::ThreeDays;

        let now = Utc::now();
        let lookback = chrono::Duration::days(3);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 1-week timeframe
    async fn backfill_1w_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "1w";
        let interval = KlineInterval::OneWeek;

        let now = Utc::now();
        let lookback = chrono::Duration::weeks(1);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    // 1-month timeframe
    async fn backfill_1M_timeframe(&self, subscribed_coins: &Vec<AssetMeta>, backfill_time: i32) -> Result<()> {
        let interval_name = "1M";
        let interval = KlineInterval::OneMonth;

        let now = Utc::now();
        // Approximate a month as 30 days for the backfill
        let lookback = chrono::Duration::days(30);
        let start_time = now - lookback * backfill_time;
        let end_time = now - lookback;

        info!("Starting backfill for {} timeframe", interval_name);
        self.backfill_timeframe_volume(
            interval_name,
            &interval,
            subscribed_coins,
            start_time,
            end_time
        ).await?;

        info!("Completed {} timeframe backfill", interval_name);
        Ok(())
    }

    /// Backfill volume and trade data for a specific timeframe
    async fn backfill_timeframe_volume(
        &self,
        interval_name: &str,
        interval: &KlineInterval,
        coins: &Vec<AssetMeta>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<()> {
        info!("Backfilling volume data for {} timeframe from {} to {}",
            interval_name,
            start_time.format("%Y-%m-%d %H:%M:%S"),
            end_time.format("%Y-%m-%d %H:%M:%S")
        );

        // Process each coin
        let mut total_updated = 0;
        let table_name = format!("ohlc_{}", interval_name);

        // Process in smaller batches to distribute load
        for batch in coins.chunks(self.batch_size) {
            // Wait between batches to avoid rate limiting
            tokio::time::sleep(Duration::from_secs(1)).await;

            // For each batch, collect all volume updates
            let mut volume_cases = Vec::new();
            let mut trades_cases = Vec::new();
            let mut where_conditions = Vec::new();
            let mut batch_updates = 0;

            // Process each coin in the batch
            for coin in batch {
                // Add delay between processing each coin
                tokio::time::sleep(Duration::from_secs(2)).await;

                let symbol = &coin.name;
                info!("Fetching {} candles for {}", interval_name, symbol);

                // Fetch historical data
                let start_millis = start_time.timestamp_millis() as u64;
                let end_millis = end_time.timestamp_millis() as u64;

                // Log the fetch range
                info!("Fetching candles for {} from {} to {}",
                    symbol,
                    start_time.format("%Y-%m-%d %H:%M:%S"),
                    end_time.format("%Y-%m-%d %H:%M:%S")
                );

                // Implement retry with exponential backoff for rate limiting
                let mut retry_count = 0;
                let max_retries = 5;
                let mut candles = Vec::new();

                while retry_count < max_retries {
                    match self.range_service.hyperliquid_data_manager.client
                        .klines_history(symbol.clone(), start_millis, end_millis, interval)
                        .await
                    {
                        Ok(result) => {
                            candles = result;
                            break;
                        },
                        Err(e) => {
                            if e.to_string().contains("429") {
                                retry_count += 1;
                                let backoff_secs = 2u64.pow(retry_count); // Exponential backoff
                                tracing::warn!(
                                    "Rate limit exceeded for {}, retry {}/{}. Waiting for {} seconds...",
                                    symbol, retry_count, max_retries, backoff_secs
                                );
                                tokio::time::sleep(Duration::from_secs(backoff_secs)).await;

                                if retry_count == max_retries {
                                    tracing::error!("Max retries reached for {}, skipping", symbol);
                                    break;
                                }
                            } else {
                                return Err(e.into());
                            }
                        }
                    }
                }

                if candles.is_empty() {
                    info!("No candles found for {} {}", symbol, interval_name);
                    continue;
                }

                info!("Processing {} candles for {} {}", candles.len(), symbol, interval_name);

                // Process each candle
                for candle in candles {
                    let time_open = DateTime::<Utc>::from_timestamp_millis(candle.time_open as i64)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S.000").to_string())
                        .unwrap_or_else(|| {
                            tracing::warn!("Invalid timestamp: {}", candle.time_open);
                            "1970-01-01 00:00:00.000".to_string()
                        });

                    let volume = candle.vlm.parse::<f64>().unwrap_or_default();
                    let num_trades = candle.num_trades;

                    volume_cases.push(format!(
                        "(time_open, symbol) = ('{}', '{}'), toFloat64({})",
                        time_open, symbol, volume
                    ));

                    trades_cases.push(format!(
                        "(time_open, symbol) = ('{}', '{}'), toUInt64({})",
                        time_open, symbol, num_trades
                    ));

                    where_conditions.push(format!(
                        "('{}', '{}')",
                        time_open, symbol
                    ));

                    batch_updates += 1;

                    // Limit batch size to avoid excessive query length
                    if batch_updates >= 100 {
                        // Execute current batch
                        self.execute_volume_update(
                            &table_name, &volume_cases, &trades_cases, &where_conditions
                        ).await?;

                        total_updated += batch_updates;
                        info!("Updated {} records for {} timeframe", batch_updates, interval_name);

                        // Reset for next batch
                        volume_cases.clear();
                        trades_cases.clear();
                        where_conditions.clear();
                        batch_updates = 0;
                    }
                }
            }

            // Process any remaining updates
            if batch_updates > 0 {
                self.execute_volume_update(
                    &table_name, &volume_cases, &trades_cases, &where_conditions
                ).await?;

                total_updated += batch_updates;
                info!("Updated {} records for {} timeframe", batch_updates, interval_name);
            }
        }

        info!("Completed volume backfill for {} timeframe, updated {} records", interval_name, total_updated);
        Ok(())
    }

    /// Execute volume update query
    async fn execute_volume_update(
        &self,
        table_name: &str,
        volume_cases: &Vec<String>,
        trades_cases: &Vec<String>,
        where_conditions: &Vec<String>
    ) -> Result<()> {
        if volume_cases.is_empty() {
            return Ok(());
        }

        let update_query = format!(
            "ALTER TABLE {} UPDATE
             total_volume = multiIf({}, total_volume),
             num_trades = multiIf({}, num_trades)
             WHERE (time_open, symbol) IN ({})",
            table_name,
            volume_cases.join(",\n            "),
            trades_cases.join(",\n            "),
            where_conditions.join(",\n            ")
        );

        self.clickhouse_client.execute_query(&update_query).await?;
        Ok(())
    }

    // Schedule periodic volume backfills
    // Schedule periodic volume backfills
    pub async fn run_periodic_volume_backfills(
        &self,
        subscribed_coins: Vec<AssetMeta>,
        self_clone: BackfillOhlcService,
    ) -> Result<()> {
        info!("Starting periodic volume backfill scheduler with individual timeframes");

        // Shared state for scheduling
        let coins = Arc::new(subscribed_coins);

        // Create a semaphore to limit concurrent API requests across all timeframes
        // Adjust the permit count based on API rate limits (start with a conservative value)
        let semaphore = Arc::new(tokio::sync::Semaphore::new(2));

        // Initial backfill for all timeframes
        info!("Starting initial volume backfill for all timeframes");
        if let Err(e) = self.backfill_all_timeframe_volumes(coins.to_vec()).await {
            error!("Initial volume backfill failed: {}", e);
        }

        // Create tasks for each interval group with appropriate frequencies
        let coins_3m = coins.clone();
        let coins_5m = coins.clone();
        let coins_15m = coins.clone();
        let coins_30m = coins.clone();
        let coins_1h = coins.clone();
        let coins_2h = coins.clone();
        let coins_4h = coins.clone();
        let coins_8h = coins.clone();
        let coins_12h = coins.clone();
        let coins_1d = coins.clone();
        let coins_3d = coins.clone();
        let coins_1w = coins.clone();
        let coins_1M = coins.clone();

        let self_3m = self_clone.clone();
        let self_5m = self_clone.clone();
        let self_15m = self_clone.clone();
        let self_30m = self_clone.clone();
        let self_1h = self_clone.clone();
        let self_2h = self_clone.clone();
        let self_4h = self_clone.clone();
        let self_8h = self_clone.clone();
        let self_12h = self_clone.clone();
        let self_1d = self_clone.clone();
        let self_3d = self_clone.clone();
        let self_1w = self_clone.clone();
        let self_1M = self_clone;

        // Share the semaphore across all tasks
        let sem_3m = semaphore.clone();
        let sem_5m = semaphore.clone();
        let sem_15m = semaphore.clone();
        let sem_30m = semaphore.clone();
        let sem_1h = semaphore.clone();
        let sem_2h = semaphore.clone();
        let sem_4h = semaphore.clone();
        let sem_8h = semaphore.clone();
        let sem_12h = semaphore.clone();
        let sem_1d = semaphore.clone();
        let sem_3d = semaphore.clone();
        let sem_1w = semaphore.clone();
        let sem_1M = semaphore.clone();

        // 3-minute interval - run every 3 minutes
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 3 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 3m timeframe backfill");
                // Acquire permit from semaphore before making API requests
                let _permit = sem_3m.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_3m.backfill_3m_timeframe(&coins_3m, 20).await {
                    error!("Periodic 3m backfill failed: {}", e);
                }
                // Permit is automatically released when _permit goes out of scope
            }
        });

        // 5-minute interval - run every 5 minutes
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 5 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 5m timeframe backfill");
                let _permit = sem_5m.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_5m.backfill_5m_timeframe(&coins_5m, 12).await {
                    error!("Periodic 5m backfill failed: {}", e);
                }
            }
        });

        // 15-minute interval - run every 15 minutes
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 15 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 15m timeframe backfill");
                let _permit = sem_15m.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_15m.backfill_15m_timeframe(&coins_15m, 4).await {
                    error!("Periodic 15m backfill failed: {}", e);
                }
            }
        });

        // 30-minute interval - run every 30 minutes
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 30 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 30m timeframe backfill");
                let _permit = sem_30m.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_30m.backfill_30m_timeframe(&coins_30m, 4).await {
                    error!("Periodic 30m backfill failed: {}", e);
                }
            }
        });

        // 1-hour interval - run every 1 hour
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 1h timeframe backfill");
                let _permit = sem_1h.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_1h.backfill_1h_timeframe(&coins_1h, 4).await {
                    error!("Periodic 1h backfill failed: {}", e);
                }
            }
        });

        // 2-hour interval - run every 2 hours
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 2 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 2h timeframe backfill");
                let _permit = sem_2h.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_2h.backfill_2h_timeframe(&coins_2h, 4).await {
                    error!("Periodic 2h backfill failed: {}", e);
                }
            }
        });

        // 4-hour interval - run every 4 hours
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 4 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 4h timeframe backfill");
                let _permit = sem_4h.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_4h.backfill_4h_timeframe(&coins_4h, 3).await {
                    error!("Periodic 4h backfill failed: {}", e);
                }
            }
        });

        // 8-hour interval - run every 8 hours
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 8 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 8h timeframe backfill");
                let _permit = sem_8h.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_8h.backfill_8h_timeframe(&coins_8h, 3).await {
                    error!("Periodic 8h backfill failed: {}", e);
                }
            }
        });

        // 12-hour interval - run every 12 hours
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 12 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 12h timeframe backfill");
                let _permit = sem_12h.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_12h.backfill_12h_timeframe(&coins_12h, 3).await {
                    error!("Periodic 12h backfill failed: {}", e);
                }
            }
        });

        // 1-day interval - run every 24 hours
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 24 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 1d timeframe backfill");
                let _permit = sem_1d.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_1d.backfill_1d_timeframe(&coins_1d, 3).await {
                    error!("Periodic 1d backfill failed: {}", e);
                }
            }
        });

        // 3-day interval - run every 3 days
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 3 * 24 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 3d timeframe backfill");
                let _permit = sem_3d.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_3d.backfill_3d_timeframe(&coins_3d, 3).await {
                    error!("Periodic 3d backfill failed: {}", e);
                }
            }
        });

        // 1-week interval - run every 7 days
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 7 * 24 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 1w timeframe backfill");
                let _permit = sem_1w.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_1w.backfill_1w_timeframe(&coins_1w, 3).await {
                    error!("Periodic 1w backfill failed: {}", e);
                }
            }
        });

        // 1-month interval - run every 30 days
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(2 * 30 * 24 * 60 * 60));
            loop {
                interval.tick().await;
                info!("Running periodic 1M timeframe backfill");
                let _permit = sem_1M.acquire().await.expect("Failed to acquire semaphore permit");
                if let Err(e) = self_1M.backfill_1M_timeframe(&coins_1M, 3).await {
                    error!("Periodic 1M backfill failed: {}", e);
                }
            }
        });

        // Main loop - just for keeping the function alive
        let mut main_interval = interval(Duration::from_secs(2 * 24 * 60 * 60));
        loop {
            main_interval.tick().await;
            info!("Volume backfill scheduler still running");
        }
    }
}
