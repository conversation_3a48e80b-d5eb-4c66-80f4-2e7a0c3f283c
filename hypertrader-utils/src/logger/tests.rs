#[cfg(test)]
mod tests {
    use crate::logger::init_logger;
    use tracing::{error, warn, info, debug};
    use tracing_subscriber::EnvFilter;

    #[test]
    fn test_logging_configuration() {
        // Initialize logger for testing
        init_logger();

        // Test different log levels
        error!("This is an error message");
        warn!("This is a warning message");
        info!("This is an info message");
        debug!("This is a debug message");

        // Test module-specific logging
        tracing::error!(target: "hypertrader_data::cex::hyperliquid", "Rate limit error - should be filtered");
        tracing::warn!(target: "hypertrader_data::cex::hyperliquid", "Rate limit warning - should show");
        tracing::info!(target: "hypertrader_services", "Service info - should show");
    }

    #[test]
    fn test_env_filter_parsing() {
        // Test that our default filter configuration is valid
        let filter = EnvFilter::new("info")
            .add_directive("hypertrader_data::cex::hyperliquid=warn".parse().unwrap())
            .add_directive("hypertrader_hyperliquid::client=warn".parse().unwrap())
            .add_directive("hypertrader_services::bin::range_updater=warn".parse().unwrap())
            .add_directive("hypertrader_services=info".parse().unwrap())
            .add_directive("hypertrader_data=info".parse().unwrap());

        // If we get here without panicking, the filter configuration is valid
        assert!(true);
    }
}
