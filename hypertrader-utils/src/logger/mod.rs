use tracing_subscriber::prelude::*;
use tracing_subscriber::{EnvFilter, fmt};

#[cfg(test)]
mod tests;

pub fn init_logger() {
    // Create a more sophisticated filter that allows different log levels for different modules
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| {
            // Default filter configuration
            EnvFilter::new("info")
                // Reduce noise from rate limiting and retry scenarios
                .add_directive("hypertrader_data::cex::hyperliquid=warn".parse().unwrap())
                .add_directive("hypertrader_hyperliquid::client=warn".parse().unwrap())
                .add_directive("hypertrader_services::bin::range_updater=warn".parse().unwrap())
                // Keep important application logs at info level
                .add_directive("hypertrader_services=info".parse().unwrap())
                .add_directive("hypertrader_data=info".parse().unwrap())
        });

    tracing_subscriber::registry()
        .with(filter)
        .with(fmt::layer().with_target(true).with_thread_ids(true))
        .init();
}
