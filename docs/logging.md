# Logging Configuration

## Overview

The hypertrader project uses the `tracing` crate for structured logging with configurable log levels per module.

## Default Configuration

By default, the logging system is configured to:
- Set INFO level for most modules
- Set WARN level for modules that tend to be noisy (rate limiting, retries)
- Include target module names and thread IDs in log output

## Environment Variable Configuration

You can override the default logging configuration using the `RUST_LOG` environment variable:

```bash
# Set all modules to debug level
export RUST_LOG=debug

# Set specific modules to different levels
export RUST_LOG="info,hypertrader_data::cex::hyperliquid=warn,hypertrader_services::bin::range_updater=debug"

# Reduce noise from rate limiting while keeping other logs at info
export RUST_LOG="info,hypertrader_data::cex::hyperliquid=warn"
```

## Module-Specific Log Levels

### Noisy Modules (Default: WARN)
These modules are set to WARN level by default to reduce noise from expected operational events:

- `hypertrader_data::cex::hyperliquid` - Rate limiting and retry messages
- `hypertrader_hyperliquid::client` - API client retry logic
- `hypertrader_services::bin::range_updater` - Backfill retry operations

### Important Modules (Default: INFO)
These modules remain at INFO level to capture important operational information:

- `hypertrader_services` - Main service operations
- `hypertrader_data` - Data processing operations

## Log Level Guidelines

### ERROR
Use for critical errors that require immediate attention:
- Database connection failures
- Critical API failures that affect core functionality
- Configuration errors that prevent startup

### WARN
Use for expected operational issues that don't require immediate action:
- Rate limiting (API 429 responses)
- Retry exhaustion for non-critical operations
- Temporary network issues

### INFO
Use for important operational information:
- Service startup/shutdown
- Successful completion of major operations
- Configuration changes

### DEBUG
Use for detailed troubleshooting information:
- Detailed API request/response data
- Internal state changes
- Performance metrics

## Examples

### Production Environment
```bash
# Minimal logging for production
export RUST_LOG="warn,hypertrader_services=info"
```

### Development Environment
```bash
# Detailed logging for development
export RUST_LOG="debug,hypertrader_data::cex::hyperliquid=info"
```

### Troubleshooting Rate Limiting
```bash
# Show rate limiting details while keeping other noise down
export RUST_LOG="warn,hypertrader_data::cex::hyperliquid=debug"
```

## Recent Changes

- Changed "Max retries reached" messages from ERROR to WARN level
- Added module-specific filtering to reduce noise from rate limiting
- Enhanced log format to include target module and thread information
