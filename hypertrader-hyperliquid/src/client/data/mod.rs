use hyperliquid_rust_sdk::{
    CandlesSnapshotResponse, H160, InfoClient, Message, Meta, OpenOrdersResponse,
    OrderStatusResponse, Subscription, UserStateResponse, UserVaultEquity, VaultDetailsResponse,
};
use hypertrader_models::kline::interval::KlineInterval;
use std::{str::FromStr, sync::Arc};
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
pub mod models;

#[derive(Debug, Clone)]
pub struct DataClient {
    pub client: Arc<tokio::sync::RwLock<InfoClient>>,
    // exchange pair info
    pub meta: Arc<tokio::sync::RwLock<Meta>>,
    pub message_sender: UnboundedSender<Message>,

    pub is_testnet: bool,
}

impl DataClient {
    pub async fn new(is_testnet: bool) -> anyhow::Result<(Self, UnboundedReceiver<Message>)> {
        let client = InfoClient::new(
            None,
            if is_testnet {
                Some(hyperliquid_rust_sdk::BaseUrl::Testnet)
            } else {
                Some(hyperliquid_rust_sdk::BaseUrl::Mainnet)
            },
            None,
        )
        .await?;

        // Retry logic for initial metadata fetch with exponential backoff
        let max_retries = 5;
        let mut retry_count = 0;
        let mut meta = None;

        while retry_count < max_retries {
            match client.meta().await {
                Ok(m) => {
                    meta = Some(m);
                    tracing::info!("Successfully fetched initial metadata");
                    break;
                },
                Err(err) => {
                    if err.to_string().contains("429") {
                        retry_count += 1;
                        let backoff_secs = 2u64.pow(retry_count); // Exponential backoff: 2, 4, 8, 16, 32 seconds
                        tracing::warn!(
                            "Rate limit exceeded during client initialization, retry {}/{}. Waiting for {} seconds...",
                            retry_count, max_retries, backoff_secs
                        );
                        tokio::time::sleep(std::time::Duration::from_secs(backoff_secs)).await;

                        if retry_count == max_retries {
                            tracing::warn!("Max retries reached for initial metadata fetch, using fallback");
                            return Err(anyhow::anyhow!("Max retries reached for initial metadata fetch: {}", err));
                        }
                    } else {
                        return Err(anyhow::anyhow!("Failed to fetch initial metadata: {}", err));
                    }
                }
            }
        }

        let meta = meta.ok_or_else(|| anyhow::anyhow!("Failed to fetch metadata after retries"))?;
        // tracing::info!("meta: {:#?}", meta);

        // 创建接收通道
        let (tx, rx) = tokio::sync::mpsc::unbounded_channel::<Message>();

        Ok((
            Self {
                client: Arc::new(tokio::sync::RwLock::new(client)),
                meta: Arc::new(tokio::sync::RwLock::new(meta)),
                message_sender: tx,
                is_testnet,
            },
            rx,
        ))
    }

    pub async fn klines_history(
        &self,
        coin: String,
        start: u64,
        end: u64,
        interval: &KlineInterval,
    ) -> anyhow::Result<Vec<CandlesSnapshotResponse>> {
        let klines = self
            .client
            .read()
            .await
            .candles_snapshot(coin, interval.to_string(), start, end)
            .await?;
        Ok(klines)
    }

    pub async fn subscribe_coin(
        &self,
        coin: String,
        interval: KlineInterval,
    ) -> anyhow::Result<u32> {
        let subscription_id = self
            .client
            .write()
            .await
            .subscribe(
                Subscription::Candle {
                    coin,
                    interval: interval.to_string(),
                },
                self.message_sender.clone(),
            )
            .await?;

        Ok(subscription_id)
    }

    pub async fn subscribe_user(&self, user: H160) -> anyhow::Result<u32> {
        let subscription_id = self
            .client
            .write()
            .await
            .subscribe(Subscription::WebData2 { user }, self.message_sender.clone())
            .await?;

        Ok(subscription_id)
    }

    pub async fn unsubscribe(&self, subscription_id: u32) -> anyhow::Result<()> {
        self.client
            .write()
            .await
            .unsubscribe(subscription_id)
            .await?;
        Ok(())
    }

    pub async fn subscribe_all_mids(&self) -> anyhow::Result<()> {
        self.client
            .write()
            .await
            .subscribe(Subscription::AllMids, self.message_sender.clone())
            .await?;
        Ok(())
    }

    pub async fn meta(&self) -> Meta {
        self.meta.read().await.clone()
    }

    // pub async fn sub_accounts(&self, address: H160) -> anyhow::Result<Vec<serde_json::Value>> {
    pub async fn sub_accounts(&self, address: H160) -> anyhow::Result<serde_json::Value> {
        let accounts = self.client.read().await.sub_accounts(address).await?;

        tracing::info!("sub_accounts: {:#?}", accounts);
        Ok(accounts)
    }

    pub async fn user_role(&self, address: H160) -> anyhow::Result<serde_json::Value> {
        let role = self.client.read().await.user_role(address).await?;
        Ok(role)
    }

    // 用户vault资产， 可以用来获取关联的vaults
    pub async fn user_vault_equities(&self, address: H160) -> anyhow::Result<Vec<UserVaultEquity>> {
        let vaults = self
            .client
            .read()
            .await
            .user_vault_equities(address)
            .await?;
        Ok(vaults)
    }

    pub async fn user_vault_equities_with_details(
        &self,
        address: H160,
    ) -> anyhow::Result<Vec<VaultDetailsResponse>> {
        let vaults = self
            .client
            .read()
            .await
            .user_vault_equities(address)
            .await?;

        let mut tasks = vec![];
        for vault in vaults {
            tasks.push(self.vault_details(H160::from_str(&vault.vault_address)?, Some(address)));
        }

        futures::future::try_join_all(tasks).await
    }

    pub async fn vault_details(
        &self,
        address: H160,
        user: Option<H160>,
    ) -> anyhow::Result<VaultDetailsResponse> {
        let details = self
            .client
            .read()
            .await
            .vault_details(address, user)
            .await?;
        Ok(details)
    }

    pub async fn update_meta(&self) -> anyhow::Result<()> {
        let meta = self.client.read().await.meta().await?;
        *self.meta.write().await = meta;
        Ok(())
    }

    // coin name string, e.g. "ORDI", "BTC", "ETH"
    pub async fn symbols(&self) -> Vec<String> {
        self.meta
            .read()
            .await
            .universe
            .iter()
            .map(|u| u.name.clone())
            .collect()
    }

    // get max leverage for a symbol
    pub async fn symbol_max_leverage(&self, symbol: &str) -> u32 {
        self.meta
            .read()
            .await
            .universe
            .iter()
            .find(|u| u.name == symbol.to_uppercase())
            .unwrap()
            .max_leverage
    }

    pub async fn symbol_min_size(&self, symbol: &str) -> u32 {
        self.meta
            .read()
            .await
            .universe
            .iter()
            .find(|u| u.name == symbol.to_uppercase())
            .unwrap()
            .sz_decimals
    }

    pub async fn open_orders(&self, address: H160) -> anyhow::Result<Vec<OpenOrdersResponse>> {
        let orders = self.client.read().await.open_orders(address).await?;
        Ok(orders)
    }

    pub async fn open_orders_detailed(
        &self,
        address: H160,
    ) -> anyhow::Result<Vec<OrderStatusResponse>> {
        let orders = self.client.read().await.open_orders(address).await?;
        tracing::info!("open orders: {orders:#?}");

        let mut tasks = vec![];
        for order in orders {
            tasks.push(self.oper_detail(address, order.oid));
        }
        let order_statuses = futures::future::try_join_all(tasks).await?;

        Ok(order_statuses)
    }

    pub async fn oper_detail(
        &self,
        address: H160,
        oid: u64,
    ) -> anyhow::Result<OrderStatusResponse> {
        let order = self
            .client
            .read()
            .await
            .query_order_by_oid(address, oid)
            .await?;
        Ok(order)
    }

    pub async fn user_data(&self, address: H160) -> anyhow::Result<UserStateResponse> {
        let user_data = self.client.read().await.user_state(address).await?;
        Ok(user_data)
    }
}
